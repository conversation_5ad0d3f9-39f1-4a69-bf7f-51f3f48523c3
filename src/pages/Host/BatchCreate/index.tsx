import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { FooterToolbar, PageContainer, ProForm } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, Space, Table, Typography, Upload } from 'antd';
import React from 'react';
import styles from './index.less';

const { Title, Text, Paragraph } = Typography;

const BatchCreateHost: React.FC = () => {
  const { hostTypeId } = useParams();

  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '可见名称',
      dataIndex: 'visibleName',
      key: 'visibleName',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: '对象组',
      dataIndex: 'group',
      key: 'group',
    },
    {
      title: '是否启用',
      dataIndex: 'enabled',
      key: 'enabled',
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
  ];

  return (
    <PageContainer header={{ title: false }}>
      <ProForm
        submitter={{
          searchConfig: {
            submitText: '保存',
            resetText: '取消',
          },
          onReset: () => {
            history.go(-1);
          },
          render: (_, doms) => {
            return <FooterToolbar>{doms}</FooterToolbar>;
          },
          // submitButtonProps: {
          //   loading: saveLoading
          // },
        }}
        onFinish={async () => {
          // const form = {
          //   name: values.name,
          //   description: values.description,
          //   hostIds: values.hosts?.map((i: API.HostPageVO) => i.id),
          // };
          // if (isEditPage) {
          //   edit({ id }, form);
          // } else {
          //   save(form);
          // }
        }}
      >
        <Typography className={styles.batchCreateHost}>
          <Title level={4} className={styles.section}>
            <Space>
              <Text type="secondary" strong className={styles.stepNumber}>
                step1
              </Text>
              <span>下载模板</span>
            </Space>
          </Title>

          <Paragraph>
            点击“<strong className={styles.downloadTemplate}>下载模板</strong>
            ”获取标准的信息导入模板。
          </Paragraph>
          <Paragraph>
            请根据实际情况完整、准确地填写模板中的每一项内容。模板中包含所有必填字段（如名称、对象组、类型等），并提供了示例数据供参考。
          </Paragraph>
          <Paragraph className={styles.section}>
            填写完成后，请确认保存文件为 Excel 格式（.xlsx），并点击<strong>“导入”</strong>
            按钮进行上传。
          </Paragraph>

          <Paragraph className={styles.section}>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={() => {
                window.open('/api/v1/rump/monitoring-object/download/template/' + hostTypeId);
              }}
            >
              下载模板
            </Button>
          </Paragraph>

          <Title level={4} className={styles.section}>
            <Space>
              <Text type="secondary" strong className={styles.stepNumber}>
                step2
              </Text>
              <span>批量导入</span>
            </Space>
          </Title>

          <Paragraph>
            <Text>请选择导入模板：</Text>
            <Upload>
              <Button>选择文件</Button>
            </Upload>
          </Paragraph>

          <Paragraph>
            <Button type="primary" icon={<UploadOutlined />}>
              导入
            </Button>
          </Paragraph>

          <Table size="middle" columns={columns} dataSource={[]} />
        </Typography>
      </ProForm>
    </PageContainer>
  );
};

export default BatchCreateHost;
